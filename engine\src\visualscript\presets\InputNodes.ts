/**
 * 输入相关的可视化脚本节点
 */

import { FunctionNode } from '../nodes/FunctionNode';
import { EventNode } from '../nodes/EventNode';
import { NodeCategory, SocketType, SocketDirection } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';

/**
 * 键盘输入节点
 */
export class KeyboardInputNode extends VisualScriptNode {
  constructor() {
    super('KeyboardInput', '键盘输入');
    this.addInput('key', 'string', '按键');
    this.addOutput('pressed', 'boolean', '是否按下');
    this.addOutput('justPressed', 'boolean', '刚按下');
    this.addOutput('justReleased', 'boolean', '刚释放');
  }

  public execute(inputs: any): any {
    const key = inputs.key;
    if (!key) return { pressed: false, justPressed: false, justReleased: false };

    // 这里应该连接到实际的输入系统
    // 暂时返回模拟数据
    return {
      pressed: false,
      justPressed: false,
      justReleased: false
    };
  }
}

/**
 * 鼠标输入节点
 */
export class MouseInputNode extends VisualScriptNode {
  constructor() {
    super('MouseInput', '鼠标输入');
    this.addInput('button', 'number', '鼠标按钮');
    this.addOutput('pressed', 'boolean', '是否按下');
    this.addOutput('position', 'vector2', '鼠标位置');
    this.addOutput('delta', 'vector2', '移动增量');
  }

  public execute(inputs: any): any {
    // 这里应该连接到实际的输入系统
    // 暂时返回模拟数据
    return {
      pressed: false,
      position: { x: 0, y: 0 },
      delta: { x: 0, y: 0 }
    };
  }
}

/**
 * 触摸输入节点
 */
export class TouchInputNode extends VisualScriptNode {
  constructor() {
    super('TouchInput', '触摸输入');
    this.addOutput('touching', 'boolean', '是否触摸');
    this.addOutput('position', 'vector2', '触摸位置');
    this.addOutput('touchCount', 'number', '触摸点数量');
  }

  public execute(): any {
    // 这里应该连接到实际的输入系统
    // 暂时返回模拟数据
    return {
      touching: false,
      position: { x: 0, y: 0 },
      touchCount: 0
    };
  }
}

/**
 * 游戏手柄输入节点
 */
export class GamepadInputNode extends VisualScriptNode {
  constructor() {
    super('GamepadInput', '游戏手柄输入');
    this.addInput('gamepadIndex', 'number', '手柄索引');
    this.addInput('button', 'number', '按钮');
    this.addOutput('pressed', 'boolean', '是否按下');
    this.addOutput('leftStick', 'vector2', '左摇杆');
    this.addOutput('rightStick', 'vector2', '右摇杆');
  }

  public execute(inputs: any): any {
    // 这里应该连接到实际的输入系统
    // 暂时返回模拟数据
    return {
      pressed: false,
      leftStick: { x: 0, y: 0 },
      rightStick: { x: 0, y: 0 }
    };
  }
}

/**
 * 按键按下节点 (150)
 */
export class IsKeyDownNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加按键输入
    this.addInput({
      name: 'key',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '按键代码',
      defaultValue: 'Space'
    });

    // 添加输出插槽
    this.addOutput({
      name: 'isDown',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否按下'
    });

    this.addOutput({
      name: 'justPressed',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '刚刚按下'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const key = this.getInputValue('key') as string;

    if (!key) {
      this.setOutputValue('isDown', false);
      this.setOutputValue('justPressed', false);
      return { isDown: false, justPressed: false };
    }

    try {
      // 这里应该连接到实际的输入系统
      // 暂时使用浏览器的键盘API模拟
      const isDown = this.isKeyCurrentlyDown(key);
      const justPressed = this.isKeyJustPressed(key);

      this.setOutputValue('isDown', isDown);
      this.setOutputValue('justPressed', justPressed);

      return { isDown, justPressed };
    } catch (error) {
      console.error('检查按键状态失败:', error);
      this.setOutputValue('isDown', false);
      this.setOutputValue('justPressed', false);
      return { isDown: false, justPressed: false };
    }
  }

  /**
   * 检查按键是否当前按下
   * @param key 按键代码
   * @returns 是否按下
   */
  private isKeyCurrentlyDown(key: string): boolean {
    // 这里应该连接到实际的输入管理器
    // 暂时返回false
    return false;
  }

  /**
   * 检查按键是否刚刚按下
   * @param key 按键代码
   * @returns 是否刚刚按下
   */
  private isKeyJustPressed(key: string): boolean {
    // 这里应该连接到实际的输入管理器
    // 暂时返回false
    return false;
  }
}

/**
 * 按键释放节点 (151)
 */
export class IsKeyUpNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加按键输入
    this.addInput({
      name: 'key',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '按键代码',
      defaultValue: 'Space'
    });

    // 添加输出插槽
    this.addOutput({
      name: 'isUp',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否释放'
    });

    this.addOutput({
      name: 'justReleased',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '刚刚释放'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const key = this.getInputValue('key') as string;

    if (!key) {
      this.setOutputValue('isUp', true);
      this.setOutputValue('justReleased', false);
      return { isUp: true, justReleased: false };
    }

    try {
      // 这里应该连接到实际的输入系统
      const isUp = this.isKeyCurrentlyUp(key);
      const justReleased = this.isKeyJustReleased(key);

      this.setOutputValue('isUp', isUp);
      this.setOutputValue('justReleased', justReleased);

      return { isUp, justReleased };
    } catch (error) {
      console.error('检查按键状态失败:', error);
      this.setOutputValue('isUp', true);
      this.setOutputValue('justReleased', false);
      return { isUp: true, justReleased: false };
    }
  }

  /**
   * 检查按键是否当前释放
   * @param key 按键代码
   * @returns 是否释放
   */
  private isKeyCurrentlyUp(key: string): boolean {
    // 这里应该连接到实际的输入管理器
    // 暂时返回true
    return true;
  }

  /**
   * 检查按键是否刚刚释放
   * @param key 按键代码
   * @returns 是否刚刚释放
   */
  private isKeyJustReleased(key: string): boolean {
    // 这里应该连接到实际的输入管理器
    // 暂时返回false
    return false;
  }
}

/**
 * 注册输入节点
 * @param registry 节点注册表
 */
export function registerInputNodes(registry: NodeRegistry): void {
  // 注册原有的输入节点（保持兼容性）
  registry.registerNodeType({
    type: 'input/keyboard',
    category: NodeCategory.INPUT,
    constructor: KeyboardInputNode,
    label: '键盘输入',
    description: '获取键盘输入状态',
    icon: 'keyboard',
    color: '#1890FF',
    tags: ['input', 'keyboard']
  });

  registry.registerNodeType({
    type: 'input/mouse',
    category: NodeCategory.INPUT,
    constructor: MouseInputNode,
    label: '鼠标输入',
    description: '获取鼠标输入状态',
    icon: 'mouse',
    color: '#1890FF',
    tags: ['input', 'mouse']
  });

  registry.registerNodeType({
    type: 'input/touch',
    category: NodeCategory.INPUT,
    constructor: TouchInputNode,
    label: '触摸输入',
    description: '获取触摸输入状态',
    icon: 'touch',
    color: '#1890FF',
    tags: ['input', 'touch']
  });

  registry.registerNodeType({
    type: 'input/gamepad',
    category: NodeCategory.INPUT,
    constructor: GamepadInputNode,
    label: '手柄输入',
    description: '获取手柄输入状态',
    icon: 'gamepad',
    color: '#1890FF',
    tags: ['input', 'gamepad']
  });

  // 注册新的按键节点 (150-151)
  registry.registerNodeType({
    type: 'input/keyboard/isKeyDown',
    category: NodeCategory.INPUT,
    constructor: IsKeyDownNode,
    label: '按键按下',
    description: '检查指定按键是否被按下',
    icon: 'key-down',
    color: '#1890FF',
    tags: ['input', 'keyboard', 'key', 'down']
  });

  registry.registerNodeType({
    type: 'input/keyboard/isKeyUp',
    category: NodeCategory.INPUT,
    constructor: IsKeyUpNode,
    label: '按键释放',
    description: '检查指定按键是否被释放',
    icon: 'key-up',
    color: '#1890FF',
    tags: ['input', 'keyboard', 'key', 'up']
  });
}
