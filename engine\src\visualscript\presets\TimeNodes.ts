/**
 * 时间相关的可视化脚本节点
 */

import { FunctionNode } from '../nodes/FunctionNode';
import { FlowNode } from '../nodes/FlowNode';
import { AsyncNode } from '../nodes/AsyncNode';
import { NodeCategory, SocketType, SocketDirection } from '../nodes/Node';
import { NodeRegistry } from '../nodes/NodeRegistry';
import { Time } from '../../utils/Time';

/**
 * 获取当前时间节点 (136)
 */
export class GetCurrentTimeNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输出插槽
    this.addOutput({
      name: 'timestamp',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '当前时间戳（毫秒）'
    });

    this.addOutput({
      name: 'seconds',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '当前时间戳（秒）'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const timestamp = Date.now();
    const seconds = timestamp / 1000;

    // 设置输出值
    this.setOutputValue('timestamp', timestamp);
    this.setOutputValue('seconds', seconds);

    return { timestamp, seconds };
  }
}

/**
 * 获取帧时间节点 (137)
 */
export class GetDeltaTimeNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输出插槽
    this.addOutput({
      name: 'deltaTime',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '帧时间（秒）'
    });

    this.addOutput({
      name: 'unscaledDeltaTime',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '未缩放帧时间（秒）'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const deltaTime = Time.getDeltaTime();
    const unscaledDeltaTime = Time.getUnscaledDeltaTime();

    // 设置输出值
    this.setOutputValue('deltaTime', deltaTime);
    this.setOutputValue('unscaledDeltaTime', unscaledDeltaTime);

    return { deltaTime, unscaledDeltaTime };
  }
}

/**
 * 延时执行节点 (138)
 */
export class DelayNode extends AsyncNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'flow',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '输入流程'
    });

    // 添加延时时间输入
    this.addInput({
      name: 'duration',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '延时时间（秒）',
      defaultValue: 1.0
    });

    // 添加输出流程插槽
    this.addOutput({
      name: 'completed',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '延时完成'
    });

    // 添加状态输出
    this.addOutput({
      name: 'state',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '节点状态'
    });
  }

  /**
   * 执行异步操作
   * @param inputs 输入值
   * @returns Promise对象
   */
  protected async executeAsync(inputs: Record<string, any>): Promise<any> {
    const duration = inputs.duration || 1.0;
    const durationMs = duration * 1000;

    return new Promise((resolve) => {
      setTimeout(() => {
        resolve({ completed: true });
      }, durationMs);
    });
  }

  /**
   * 处理完成
   * @param result 结果
   */
  protected handleComplete(result: any): void {
    super.handleComplete(result);

    // 触发完成流程
    this.triggerFlow('completed');
  }
}

/**
 * 计时器节点 (139)
 */
export class TimerNode extends FlowNode {
  private startTime: number = 0;
  private isRunning: boolean = false;
  private intervalId: NodeJS.Timeout | null = null;

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'start',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '开始计时'
    });

    this.addInput({
      name: 'stop',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '停止计时'
    });

    this.addInput({
      name: 'reset',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '重置计时器'
    });

    // 添加间隔时间输入
    this.addInput({
      name: 'interval',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '触发间隔（秒）',
      defaultValue: 1.0
    });

    // 添加输出插槽
    this.addOutput({
      name: 'tick',
      type: SocketType.FLOW,
      direction: SocketDirection.OUTPUT,
      description: '计时器触发'
    });

    this.addOutput({
      name: 'elapsed',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '已用时间（秒）'
    });

    this.addOutput({
      name: 'isRunning',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否运行中'
    });
  }

  /**
   * 处理输入并确定输出流程
   * @param inputs 输入值
   * @returns 输出流程名称
   */
  protected process(inputs: Record<string, any>): string | null {
    // 这是一个特殊的计时器节点，不通过常规流程触发
    // 而是通过特定的控制方法

    // 更新输出值
    const elapsed = this.isRunning ? (Date.now() - this.startTime) / 1000 : 0;
    this.setOutputValue('elapsed', elapsed);
    this.setOutputValue('isRunning', this.isRunning);

    return null; // 不触发任何流程输出
  }

  /**
   * 开始计时器（公共方法）
   */
  public start(): void {
    if (!this.isRunning) {
      this.startTimer();
    }
  }

  /**
   * 停止计时器（公共方法）
   */
  public stop(): void {
    this.stopTimer();
  }

  /**
   * 重置计时器（公共方法）
   */
  public reset(): void {
    this.resetTimer();
  }

  /**
   * 开始计时器
   */
  private startTimer(): void {
    this.startTime = Date.now();
    this.isRunning = true;

    const interval = this.getInputValue('interval') as number || 1.0;
    const intervalMs = interval * 1000;

    this.intervalId = setInterval(() => {
      if (this.isRunning) {
        this.triggerFlow('tick');
      }
    }, intervalMs);
  }

  /**
   * 停止计时器
   */
  private stopTimer(): void {
    this.isRunning = false;
    if (this.intervalId) {
      clearInterval(this.intervalId);
      this.intervalId = null;
    }
  }

  /**
   * 重置计时器
   */
  private resetTimer(): void {
    this.stopTimer();
    this.startTime = Date.now();
  }

  /**
   * 销毁节点
   */
  public dispose(): void {
    this.stopTimer();
    super.dispose();
  }
}

/**
 * 秒表节点 (140)
 */
export class StopwatchNode extends FlowNode {
  private startTime: number = 0;
  private pausedTime: number = 0;
  private isRunning: boolean = false;
  private isPaused: boolean = false;

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入流程插槽
    this.addInput({
      name: 'start',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '开始秒表'
    });

    this.addInput({
      name: 'pause',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '暂停秒表'
    });

    this.addInput({
      name: 'resume',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '恢复秒表'
    });

    this.addInput({
      name: 'stop',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '停止秒表'
    });

    this.addInput({
      name: 'reset',
      type: SocketType.FLOW,
      direction: SocketDirection.INPUT,
      description: '重置秒表'
    });

    // 添加输出插槽
    this.addOutput({
      name: 'elapsed',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'number',
      description: '已用时间（秒）'
    });

    this.addOutput({
      name: 'isRunning',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否运行中'
    });

    this.addOutput({
      name: 'isPaused',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'boolean',
      description: '是否暂停'
    });
  }

  /**
   * 处理输入并确定输出流程
   * @param inputs 输入值
   * @returns 输出流程名称
   */
  protected process(inputs: Record<string, any>): string | null {
    // 计算已用时间
    let elapsed = 0;
    if (this.isRunning) {
      if (this.isPaused) {
        elapsed = this.pausedTime / 1000;
      } else {
        elapsed = (this.pausedTime + (Date.now() - this.startTime)) / 1000;
      }
    }

    // 更新输出值
    this.setOutputValue('elapsed', elapsed);
    this.setOutputValue('isRunning', this.isRunning);
    this.setOutputValue('isPaused', this.isPaused);

    return null; // 不触发任何流程输出
  }

  /**
   * 开始秒表（公共方法）
   */
  public start(): void {
    if (!this.isRunning) {
      this.startStopwatch();
    }
  }

  /**
   * 暂停秒表（公共方法）
   */
  public pause(): void {
    if (this.isRunning && !this.isPaused) {
      this.pauseStopwatch();
    }
  }

  /**
   * 恢复秒表（公共方法）
   */
  public resume(): void {
    if (this.isRunning && this.isPaused) {
      this.resumeStopwatch();
    }
  }

  /**
   * 停止秒表（公共方法）
   */
  public stop(): void {
    this.stopStopwatch();
  }

  /**
   * 重置秒表（公共方法）
   */
  public reset(): void {
    this.resetStopwatch();
  }

  /**
   * 开始秒表
   */
  private startStopwatch(): void {
    this.startTime = Date.now();
    this.pausedTime = 0;
    this.isRunning = true;
    this.isPaused = false;
  }

  /**
   * 暂停秒表
   */
  private pauseStopwatch(): void {
    if (this.isRunning && !this.isPaused) {
      this.pausedTime += Date.now() - this.startTime;
      this.isPaused = true;
    }
  }

  /**
   * 恢复秒表
   */
  private resumeStopwatch(): void {
    if (this.isRunning && this.isPaused) {
      this.startTime = Date.now();
      this.isPaused = false;
    }
  }

  /**
   * 停止秒表
   */
  private stopStopwatch(): void {
    this.isRunning = false;
    this.isPaused = false;
  }

  /**
   * 重置秒表
   */
  private resetStopwatch(): void {
    this.startTime = Date.now();
    this.pausedTime = 0;
    this.isRunning = false;
    this.isPaused = false;
  }
}

/**
 * 格式化时间节点 (141)
 */
export class FormatTimeNode extends FunctionNode {
  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 添加输入插槽
    this.addInput({
      name: 'timestamp',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'number',
      description: '时间戳（毫秒）',
      defaultValue: Date.now()
    });

    this.addInput({
      name: 'format',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '格式字符串',
      defaultValue: 'YYYY-MM-DD HH:mm:ss'
    });

    this.addInput({
      name: 'locale',
      type: SocketType.DATA,
      direction: SocketDirection.INPUT,
      dataType: 'string',
      description: '语言区域',
      defaultValue: 'zh-CN'
    });

    // 添加输出插槽
    this.addOutput({
      name: 'formatted',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '格式化后的时间字符串'
    });

    this.addOutput({
      name: 'date',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '日期部分'
    });

    this.addOutput({
      name: 'time',
      type: SocketType.DATA,
      direction: SocketDirection.OUTPUT,
      dataType: 'string',
      description: '时间部分'
    });
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    const timestamp = this.getInputValue('timestamp') as number;
    const format = this.getInputValue('format') as string;
    const locale = this.getInputValue('locale') as string;

    const date = new Date(timestamp);

    // 简单的格式化实现
    let formatted = format;

    // 年份
    formatted = formatted.replace(/YYYY/g, date.getFullYear().toString());
    formatted = formatted.replace(/YY/g, date.getFullYear().toString().slice(-2));

    // 月份
    const month = date.getMonth() + 1;
    formatted = formatted.replace(/MM/g, month.toString().padStart(2, '0'));
    formatted = formatted.replace(/M/g, month.toString());

    // 日期
    const day = date.getDate();
    formatted = formatted.replace(/DD/g, day.toString().padStart(2, '0'));
    formatted = formatted.replace(/D/g, day.toString());

    // 小时
    const hours = date.getHours();
    formatted = formatted.replace(/HH/g, hours.toString().padStart(2, '0'));
    formatted = formatted.replace(/H/g, hours.toString());

    // 分钟
    const minutes = date.getMinutes();
    formatted = formatted.replace(/mm/g, minutes.toString().padStart(2, '0'));
    formatted = formatted.replace(/m/g, minutes.toString());

    // 秒
    const seconds = date.getSeconds();
    formatted = formatted.replace(/ss/g, seconds.toString().padStart(2, '0'));
    formatted = formatted.replace(/s/g, seconds.toString());

    // 分离日期和时间部分
    const dateStr = `${date.getFullYear()}-${(month).toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
    const timeStr = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;

    // 设置输出值
    this.setOutputValue('formatted', formatted);
    this.setOutputValue('date', dateStr);
    this.setOutputValue('time', timeStr);

    return { formatted, date: dateStr, time: timeStr };
  }
}

/**
 * 注册时间节点
 * @param registry 节点注册表
 */
export function registerTimeNodes(registry: NodeRegistry): void {
  // 注册获取当前时间节点 (136)
  registry.registerNodeType({
    type: 'time/getCurrentTime',
    category: NodeCategory.TIME,
    constructor: GetCurrentTimeNode,
    label: '获取当前时间',
    description: '获取当前系统时间戳',
    icon: 'clock',
    color: '#52C41A',
    tags: ['time', 'current', 'timestamp']
  });

  // 注册获取帧时间节点 (137)
  registry.registerNodeType({
    type: 'time/getDeltaTime',
    category: NodeCategory.TIME,
    constructor: GetDeltaTimeNode,
    label: '获取帧时间',
    description: '获取上一帧到当前帧的时间差',
    icon: 'clock',
    color: '#52C41A',
    tags: ['time', 'delta', 'frame']
  });

  // 注册延时执行节点 (138)
  registry.registerNodeType({
    type: 'time/delay',
    category: NodeCategory.TIME,
    constructor: DelayNode,
    label: '延时执行',
    description: '延时指定时间后执行',
    icon: 'pause',
    color: '#52C41A',
    tags: ['time', 'delay', 'wait']
  });

  // 注册计时器节点 (139)
  registry.registerNodeType({
    type: 'time/timer',
    category: NodeCategory.TIME,
    constructor: TimerNode,
    label: '计时器',
    description: '创建一个计时器',
    icon: 'timer',
    color: '#52C41A',
    tags: ['time', 'timer', 'interval']
  });

  // 注册秒表节点 (140)
  registry.registerNodeType({
    type: 'time/stopwatch',
    category: NodeCategory.TIME,
    constructor: StopwatchNode,
    label: '秒表',
    description: '创建一个秒表计时器',
    icon: 'stopwatch',
    color: '#52C41A',
    tags: ['time', 'stopwatch', 'measure']
  });

  // 注册格式化时间节点 (141)
  registry.registerNodeType({
    type: 'time/formatTime',
    category: NodeCategory.TIME,
    constructor: FormatTimeNode,
    label: '格式化时间',
    description: '将时间戳格式化为可读字符串',
    icon: 'format',
    color: '#52C41A',
    tags: ['time', 'format', 'string']
  });
}
