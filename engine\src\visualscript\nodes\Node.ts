/**
 * 视觉脚本节点基类
 */
import { EventEmitter } from '../../utils/EventEmitter';
import { ExecutionContext } from '../execution/ExecutionContext';
import { Graph } from '../graph/Graph';
import { NodeMetadata } from '../graph/GraphJSON';

/**
 * 节点插槽类型
 */
export enum SocketType {
  /** 流程插槽 */
  FLOW = 'flow',
  /** 数据插槽 */
  DATA = 'data'
}

/**
 * 节点插槽方向
 */
export enum SocketDirection {
  /** 输入插槽 */
  INPUT = 'input',
  /** 输出插槽 */
  OUTPUT = 'output'
}

/**
 * 节点插槽定义
 */
export interface SocketDefinition {
  /** 插槽名称 */
  name: string;
  /** 插槽类型 */
  type: SocketType;
  /** 插槽方向 */
  direction: SocketDirection;
  /** 数据类型（对于数据插槽） */
  dataType?: string;
  /** 默认值（对于数据插槽） */
  defaultValue?: any;
  /** 插槽描述 */
  description?: string;
  /** 是否可选 */
  optional?: boolean;
}

/**
 * 节点插槽
 */
export interface Socket extends SocketDefinition {
  /** 连接的节点ID */
  connectedNodeId?: string;
  /** 连接的插槽名称 */
  connectedSocketName?: string;
  /** 当前值 */
  value?: any;
}

/**
 * 节点连接
 */
export interface NodeConnection {
  /** 源节点 */
  sourceNode: Node;
  /** 源插槽名称 */
  sourceSocketName: string;
  /** 目标节点 */
  targetNode: Node;
  /** 目标插槽名称 */
  targetSocketName: string;
}

/**
 * 节点类型
 */
export enum NodeType {
  /** 普通节点 */
  NORMAL = 'normal',
  /** 事件节点 */
  EVENT = 'event',
  /** 函数节点 */
  FUNCTION = 'function',
  /** 异步节点 */
  ASYNC = 'async'
}

/**
 * 节点类别
 */
export enum NodeCategory {
  /** 流程控制 */
  FLOW = 'flow',
  /** 数学运算 */
  MATH = 'math',
  /** 逻辑运算 */
  LOGIC = 'logic',
  /** 字符串操作 */
  STRING = 'string',
  /** 数组操作 */
  ARRAY = 'array',
  /** 对象操作 */
  OBJECT = 'object',
  /** 变量操作 */
  VARIABLE = 'variable',
  /** 常量 */
  CONSTANT = 'constant',
  /** 函数操作 */
  FUNCTION = 'function',
  /** 事件操作 */
  EVENT = 'event',
  /** 实体操作 */
  ENTITY = 'entity',
  /** 组件操作 */
  COMPONENT = 'component',
  /** 物理操作 */
  PHYSICS = 'physics',
  /** 动画操作 */
  ANIMATION = 'animation',
  /** 输入操作 */
  INPUT = 'input',
  /** 音频操作 */
  AUDIO = 'audio',
  /** 网络操作 */
  NETWORK = 'network',
  /** AI操作 */
  AI = 'ai',
  /** 调试操作 */
  DEBUG = 'debug',
  /** UI操作 */
  UI = 'ui',
  /** 自定义操作 */
  CUSTOM = 'custom'
}

/**
 * 节点选项
 */
export interface NodeOptions {
  /** 节点ID */
  id: string;
  /** 节点类型名称 */
  type: string;
  /** 节点元数据 */
  metadata?: NodeMetadata;
  /** 所属图形 */
  graph: Graph;
  /** 执行上下文 */
  context: ExecutionContext;
}

/**
 * 节点基类
 */
export class Node extends EventEmitter {
  /** 节点ID */
  public readonly id: string;

  /** 节点类型名称 */
  public readonly type: string;

  /** 节点类型 */
  public readonly nodeType: NodeType = NodeType.NORMAL;

  /** 节点类别 */
  public readonly category: NodeCategory = NodeCategory.CUSTOM;

  /** 节点元数据 */
  public metadata: NodeMetadata;

  /** 所属图形 */
  protected graph: Graph;

  /** 执行上下文 */
  protected context: ExecutionContext;

  /** 输入插槽 */
  protected inputs: Map<string, Socket> = new Map();

  /** 输出插槽 */
  protected outputs: Map<string, Socket> = new Map();

  /** 输入连接 */
  protected inputConnections: Map<string, NodeConnection> = new Map();

  /** 输出连接 */
  protected outputConnections: Map<string, NodeConnection[]> = new Map();

  /**
   * 创建节点
   * @param options 节点选项
   */
  constructor(options: NodeOptions) {
    super();

    this.id = options.id;
    this.type = options.type;
    this.metadata = options.metadata || { positionX: 0, positionY: 0 };
    this.graph = options.graph;
    this.context = options.context;

    // 初始化插槽
    this.initializeSockets();
  }

  /**
   * 初始化插槽
   */
  protected initializeSockets(): void {
    // 子类实现
  }

  /**
   * 添加输入插槽
   * @param definition 插槽定义
   * @returns 添加的插槽
   */
  protected addInput(definition: SocketDefinition): Socket {
    const socket: Socket = {
      ...definition,
      direction: SocketDirection.INPUT,
      value: definition.defaultValue
    };

    this.inputs.set(socket.name, socket);
    return socket;
  }

  /**
   * 添加输出插槽
   * @param definition 插槽定义
   * @returns 添加的插槽
   */
  protected addOutput(definition: SocketDefinition): Socket {
    const socket: Socket = {
      ...definition,
      direction: SocketDirection.OUTPUT,
      value: definition.defaultValue
    };

    this.outputs.set(socket.name, socket);

    // 初始化输出连接数组
    this.outputConnections.set(socket.name, []);

    return socket;
  }

  /**
   * 获取输入插槽
   * @param name 插槽名称
   * @returns 插槽
   */
  public getInput(name: string): Socket | undefined {
    return this.inputs.get(name);
  }

  /**
   * 获取输出插槽
   * @param name 插槽名称
   * @returns 插槽
   */
  public getOutput(name: string): Socket | undefined {
    return this.outputs.get(name);
  }

  /**
   * 获取所有输入插槽
   * @returns 输入插槽映射
   */
  public getInputs(): Map<string, Socket> {
    return this.inputs;
  }

  /**
   * 获取所有输出插槽
   * @returns 输出插槽映射
   */
  public getOutputs(): Map<string, Socket> {
    return this.outputs;
  }

  /**
   * 获取输入插槽数组
   * @returns 输入插槽数组
   */
  public getInputSockets(): Socket[] {
    return Array.from(this.inputs.values());
  }

  /**
   * 获取节点元数据
   * @returns 节点元数据
   */
  public getMetadata(): NodeMetadata {
    return this.metadata;
  }

  /**
   * 获取节点位置
   * @returns 节点位置
   */
  public getPosition(): { x: number; y: number } {
    return {
      x: this.metadata.positionX || 0,
      y: this.metadata.positionY || 0
    };
  }

  /**
   * 检查节点是否已执行
   * @returns 是否已执行
   */
  public isExecuted(): boolean {
    // 简单实现，可以根据需要扩展
    return false;
  }

  /**
   * 设置参数值
   * @param name 参数名称
   * @param value 参数值
   */
  public setParameterValue(name: string, value: any): void {
    const input = this.inputs.get(name);

    if (input) {
      input.value = value;
    }
  }

  /**
   * 获取参数值
   * @param name 参数名称
   * @returns 参数值
   */
  public getParameterValue(name: string): any {
    const input = this.inputs.get(name);

    if (input) {
      return input.value;
    }

    return undefined;
  }

  /**
   * 连接输入
   * @param inputName 输入插槽名称
   * @param sourceNode 源节点
   * @param sourceOutputName 源输出插槽名称
   * @returns 是否连接成功
   */
  public connectInput(inputName: string, sourceNode: Node, sourceOutputName: string): boolean {
    const input = this.inputs.get(inputName);
    const sourceOutput = sourceNode.getOutput(sourceOutputName);

    if (!input || !sourceOutput) {
      return false;
    }

    // 检查类型兼容性
    if (input.type !== sourceOutput.type) {
      return false;
    }

    // 如果是数据插槽，检查数据类型兼容性
    if (input.type === SocketType.DATA &&
        input.dataType !== sourceOutput.dataType) {
      return false;
    }

    // 创建连接
    const connection: NodeConnection = {
      sourceNode: sourceNode,
      sourceSocketName: sourceOutputName,
      targetNode: this,
      targetSocketName: inputName
    };

    // 存储连接
    this.inputConnections.set(inputName, connection);

    // 更新插槽连接信息
    input.connectedNodeId = sourceNode.id;
    input.connectedSocketName = sourceOutputName;

    // 添加到源节点的输出连接
    const sourceOutputConnections = sourceNode.outputConnections.get(sourceOutputName) || [];
    sourceOutputConnections.push(connection);
    sourceNode.outputConnections.set(sourceOutputName, sourceOutputConnections);

    // 触发连接事件
    this.emit('inputConnected', inputName, sourceNode, sourceOutputName);
    sourceNode.emit('outputConnected', sourceOutputName, this, inputName);

    return true;
  }

  /**
   * 连接流程
   * @param outputName 输出插槽名称
   * @param targetNode 目标节点
   * @param targetInputName 目标输入插槽名称
   * @returns 是否连接成功
   */
  public connectFlow(outputName: string, targetNode: Node, targetInputName: string): boolean {
    const output = this.outputs.get(outputName);
    const targetInput = targetNode.getInput(targetInputName);

    if (!output || !targetInput) {
      return false;
    }

    // 检查是否为流程插槽
    if (output.type !== SocketType.FLOW || targetInput.type !== SocketType.FLOW) {
      return false;
    }

    // 创建连接
    const connection: NodeConnection = {
      sourceNode: this,
      sourceSocketName: outputName,
      targetNode: targetNode,
      targetSocketName: targetInputName
    };

    // 存储连接
    const outputConnections = this.outputConnections.get(outputName) || [];
    outputConnections.push(connection);
    this.outputConnections.set(outputName, outputConnections);

    // 更新目标插槽连接信息
    targetInput.connectedNodeId = this.id;
    targetInput.connectedSocketName = outputName;

    // 存储到目标节点的输入连接
    targetNode.inputConnections.set(targetInputName, connection);

    // 触发连接事件
    this.emit('outputConnected', outputName, targetNode, targetInputName);
    targetNode.emit('inputConnected', targetInputName, this, outputName);

    return true;
  }

  /**
   * 断开输入连接
   * @param inputName 输入插槽名称
   * @returns 是否断开成功
   */
  public disconnectInput(inputName: string): boolean {
    const connection = this.inputConnections.get(inputName);

    if (!connection) {
      return false;
    }

    // 获取源节点和源插槽
    const sourceNode = connection.sourceNode;
    const sourceSocketName = connection.sourceSocketName;

    // 从源节点的输出连接中移除
    const sourceOutputConnections = sourceNode.outputConnections.get(sourceSocketName) || [];
    const index = sourceOutputConnections.findIndex(conn =>
      conn.targetNode.id === this.id && conn.targetSocketName === inputName);

    if (index !== -1) {
      sourceOutputConnections.splice(index, 1);
      sourceNode.outputConnections.set(sourceSocketName, sourceOutputConnections);
    }

    // 更新插槽连接信息
    const input = this.inputs.get(inputName);
    if (input) {
      input.connectedNodeId = undefined;
      input.connectedSocketName = undefined;
    }

    // 移除连接
    this.inputConnections.delete(inputName);

    // 触发断开事件
    this.emit('inputDisconnected', inputName, sourceNode, sourceSocketName);
    sourceNode.emit('outputDisconnected', sourceSocketName, this, inputName);

    return true;
  }

  /**
   * 断开输出连接
   * @param outputName 输出插槽名称
   * @param targetNode 目标节点
   * @param targetInputName 目标输入插槽名称
   * @returns 是否断开成功
   */
  public disconnectOutput(outputName: string, targetNode: Node, targetInputName: string): boolean {
    const outputConnections = this.outputConnections.get(outputName) || [];

    // 查找连接
    const index = outputConnections.findIndex(conn =>
      conn.targetNode.id === targetNode.id && conn.targetSocketName === targetInputName);

    if (index === -1) {
      return false;
    }

    // 从输出连接中移除
    outputConnections.splice(index, 1);
    this.outputConnections.set(outputName, outputConnections);

    // 从目标节点的输入连接中移除
    targetNode.inputConnections.delete(targetInputName);

    // 更新目标插槽连接信息
    const targetInput = targetNode.getInput(targetInputName);
    if (targetInput) {
      targetInput.connectedNodeId = undefined;
      targetInput.connectedSocketName = undefined;
    }

    // 触发断开事件
    this.emit('outputDisconnected', outputName, targetNode, targetInputName);
    targetNode.emit('inputDisconnected', targetInputName, this, outputName);

    return true;
  }

  /**
   * 断开所有连接
   */
  public disconnectAll(): void {
    // 断开所有输入连接
    for (const inputName of this.inputConnections.keys()) {
      this.disconnectInput(inputName);
    }

    // 断开所有输出连接
    for (const [outputName, connections] of this.outputConnections.entries()) {
      // 复制连接数组，因为在断开过程中会修改原数组
      const connectionsToDisconnect = [...connections];

      for (const connection of connectionsToDisconnect) {
        this.disconnectOutput(outputName, connection.targetNode, connection.targetSocketName);
      }
    }
  }

  /**
   * 执行节点
   * @returns 执行结果
   */
  public execute(): any {
    // 子类实现
    return null;
  }

  /**
   * 获取输入值
   * @param name 输入名称
   * @returns 输入值
   */
  public getInputValue(name: string): any {
    const input = this.inputs.get(name);

    if (!input) {
      return undefined;
    }

    // 如果有连接，从源节点获取值
    if (input.connectedNodeId && input.connectedSocketName) {
      const sourceNode = this.graph.getNode(input.connectedNodeId);

      if (sourceNode) {
        const sourceOutput = sourceNode.getOutput(input.connectedSocketName);

        if (sourceOutput) {
          // 如果源节点是函数节点，需要执行它
          if (sourceNode.nodeType === NodeType.FUNCTION) {
            sourceNode.execute();
          }

          return sourceOutput.value;
        }
      }
    }

    // 否则返回当前值
    return input.value;
  }

  /**
   * 设置输出值
   * @param name 输出名称
   * @param value 输出值
   */
  public setOutputValue(name: string, value: any): void {
    const output = this.outputs.get(name);

    if (output) {
      output.value = value;
    }
  }

  /**
   * 触发流程输出
   * @param name 输出名称
   */
  public triggerFlow(name: string): void {
    const connections = this.outputConnections.get(name) || [];

    for (const connection of connections) {
      // 创建纤程并添加到引擎
      const fiber = this.context.engine.createFiber(this, name);
      this.context.engine.addFiber(fiber);
    }
  }

  /**
   * 初始化节点
   */
  public initialize(): void {
    // 子类实现
  }

  /**
   * 销毁节点
   */
  public dispose(): void {
    // 断开所有连接
    this.disconnectAll();

    // 清空插槽
    this.inputs.clear();
    this.outputs.clear();

    // 清空连接
    this.inputConnections.clear();
    this.outputConnections.clear();

    // 移除所有事件监听
    this.removeAllListeners();
  }
}
